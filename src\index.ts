// MySQL MCP服务器实现
import { FastMCP } from 'fastmcp';
import { z } from 'zod';
import { MySQLManager } from './mysqlManager.js';
import { StringConstants } from './constants.js';

// 创建全局MySQL管理器实例
const mysqlManager = new MySQLManager();

// 创建FastMCP服务器
const mcp = new FastMCP({
  name: StringConstants.SERVER_NAME,
  version: "1.0.0"
});

// MySQL查询工具
mcp.addTool({
  name: 'mysql_query',
  description: '执行MySQL查询 (SELECT, SHOW, DESCRIBE, 等)',
  parameters: z.object({
    query: z.string().describe('要执行的SQL查询'),
    params: z.array(z.any()).optional().describe('预处理语句的可选参数')
  }),
  execute: async (args) => {
    try {
      if (!args.params) {
        args.params = [];
      }

      // 验证参数
      mysqlManager['validateInput'](args.query, "query");
      args.params.forEach((param, i) => {
        mysqlManager['validateInput'](param, `param_${i}`);
      });

      const result = await mysqlManager.executeQuery(args.query, args.params);
      return JSON.stringify(result, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_QUERY_FAILED} ${error.message}`);
    }
  }
});

// 显示表工具
mcp.addTool({
  name: 'mysql_show_tables',
  description: '显示当前数据库中的所有表',
  parameters: z.object({}),
  execute: async () => {
    try {
      const showTablesQuery = "SHOW TABLES";
      const result = await mysqlManager.executeQuery(showTablesQuery);
      return JSON.stringify(result, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_SHOW_TABLES_FAILED} ${error.message}`);
    }
  }
});

// 描述表工具
mcp.addTool({
  name: 'mysql_describe_table',
  description: '描述指定表的结构',
  parameters: z.object({
    table_name: z.string().describe('要描述的表名')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);
      const result = await mysqlManager['getTableSchemaCached'](args.table_name);
      return JSON.stringify(result, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_DESCRIBE_TABLE_FAILED} ${error.message}`);
    }
  }
});

// 选择数据工具
mcp.addTool({
  name: 'mysql_select_data',
  description: '从表中选择数据，支持可选条件',
  parameters: z.object({
    table_name: z.string().describe('要选择数据的表名'),
    columns: z.array(z.string()).optional().describe('可选的列名列表'),
    where_clause: z.string().optional().describe('可选的WHERE子句'),
    limit: z.number().int().optional().describe('可选的行数限制')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);

      if (!args.columns) {
        args.columns = ["*"];
      }

      args.columns.forEach(col => {
        if (col !== "*") {
          mysqlManager['validateInput'](col, "column");
        }
      });

      let query = `SELECT ${args.columns.join(', ')} FROM \`${args.table_name}\``;

      if (args.where_clause) {
        mysqlManager['validateInput'](args.where_clause, "where_clause");
        query += ` WHERE ${args.where_clause}`;
      }

      if (args.limit) {
        query += ` LIMIT ${Math.floor(args.limit)}`;
      }

      const result = await mysqlManager.executeQuery(query);
      return JSON.stringify(result, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_SELECT_DATA_FAILED} ${error.message}`);
    }
  }
});

// 插入数据工具
mcp.addTool({
  name: 'mysql_insert_data',
  description: '向表中插入新数据',
  parameters: z.object({
    table_name: z.string().describe('要插入数据的表名'),
    data: z.record(z.any()).describe('列名和值的键值对')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);

      Object.keys(args.data).forEach(key => {
        mysqlManager['validateInput'](key, "column_name");
        mysqlManager['validateInput'](args.data[key], "column_value");
      });

      const columns = Object.keys(args.data);
      const values = Object.values(args.data);
      const placeholders = columns.map(() => "?").join(", ");

      const query = `INSERT INTO \`${args.table_name}\` (\`${columns.join('`, `')}\`) VALUES (${placeholders})`;
      const result = await mysqlManager.executeQuery(query, values);

      return JSON.stringify({ [StringConstants.SUCCESS_KEY]: true, ...result }, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_INSERT_DATA_FAILED} ${error.message}`);
    }
  }
});

// 更新数据工具
mcp.addTool({
  name: 'mysql_update_data',
  description: '更新表中的现有数据',
  parameters: z.object({
    table_name: z.string().describe('要更新的表名'),
    data: z.record(z.any()).describe('列名和新值的键值对'),
    where_clause: z.string().describe('WHERE子句，指定要更新的记录（不包含WHERE关键字）')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);
      mysqlManager['validateInput'](args.where_clause, "where_clause");

      Object.keys(args.data).forEach(key => {
        mysqlManager['validateInput'](key, "column_name");
        mysqlManager['validateInput'](args.data[key], "column_value");
      });

      const columns = Object.keys(args.data);
      const values = Object.values(args.data);
      const setClause = columns.map(col => `\`${col}\` = ?`).join(", ");

      const query = `UPDATE \`${args.table_name}\` SET ${setClause} WHERE ${args.where_clause}`;
      const result = await mysqlManager.executeQuery(query, values);

      return JSON.stringify({ [StringConstants.SUCCESS_KEY]: true, ...result }, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_UPDATE_DATA_FAILED} ${error.message}`);
    }
  }
});

// 删除数据工具
mcp.addTool({
  name: 'mysql_delete_data',
  description: '从表中删除数据',
  parameters: z.object({
    table_name: z.string().describe('要删除数据的表名'),
    where_clause: z.string().describe('WHERE子句，指定要删除的记录（不包含WHERE关键字）')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);
      mysqlManager['validateInput'](args.where_clause, "where_clause");

      const query = `DELETE FROM \`${args.table_name}\` WHERE ${args.where_clause}`;
      const result = await mysqlManager.executeQuery(query);

      return JSON.stringify({ [StringConstants.SUCCESS_KEY]: true, ...result }, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_DELETE_DATA_FAILED} ${error.message}`);
    }
  }
});

// 获取模式工具
mcp.addTool({
  name: 'mysql_get_schema',
  description: '获取数据库模式信息，包括表、列和约束',
  parameters: z.object({
    table_name: z.string().optional().describe('可选的特定表名，用于获取该表的模式信息')
  }),
  execute: async (args) => {
    try {
      let query = `
        SELECT
          TABLE_NAME,
          COLUMN_NAME,
          DATA_TYPE,
          IS_NULLABLE,
          COLUMN_DEFAULT,
          COLUMN_KEY,
          EXTRA,
          COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
      `;

      const params: string[] = [];
      if (args.table_name) {
        mysqlManager['validateTableName'](args.table_name);
        query += " AND TABLE_NAME = ?";
        params.push(args.table_name);
      }

      query += " ORDER BY TABLE_NAME, ORDINAL_POSITION";

      const result = await mysqlManager.executeQuery(query, params.length > 0 ? params : undefined);
      return JSON.stringify(result, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_GET_SCHEMA_FAILED} ${error.message}`);
    }
  }
});

// 获取索引工具
mcp.addTool({
  name: 'mysql_get_indexes',
  description: '获取表或所有表的索引信息',
  parameters: z.object({
    table_name: z.string().optional().describe('可选的特定表名，用于获取该表的索引信息')
  }),
  execute: async (args) => {
    try {
      if (args.table_name) {
        mysqlManager['validateTableName'](args.table_name);
        // 使用缓存版本提高性能
        const cacheKey = `indexes_${args.table_name}`;
        let cachedResult = mysqlManager['indexCache'].get(cacheKey);

        if (cachedResult === null) {
          const indexesQuery = `
            SELECT
              INDEX_NAME,
              COLUMN_NAME,
              NON_UNIQUE,
              SEQ_IN_INDEX,
              INDEX_TYPE
            FROM INFORMATION_SCHEMA.STATISTICS
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
            ORDER BY INDEX_NAME, SEQ_IN_INDEX
          `;
          const queryResult = await mysqlManager.executeQuery(indexesQuery, [args.table_name]);
          const result = JSON.stringify(queryResult, null, 2);
          mysqlManager['indexCache'].put(cacheKey, result);
          mysqlManager['metrics'].cacheMisses++;
          return result;
        } else {
          mysqlManager['metrics'].cacheHits++;
          return cachedResult;
        }
      } else {
        // 获取所有表的索引信息（不使用缓存）
        const query = `
          SELECT
            TABLE_NAME,
            INDEX_NAME,
            COLUMN_NAME,
            NON_UNIQUE,
            SEQ_IN_INDEX,
            INDEX_TYPE
          FROM INFORMATION_SCHEMA.STATISTICS
          WHERE TABLE_SCHEMA = DATABASE()
          ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX
        `;
        const result = await mysqlManager.executeQuery(query);
        return JSON.stringify(result, null, 2);
      }
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_GET_INDEXES_FAILED} ${error.message}`);
    }
  }
});

// 获取外键工具
mcp.addTool({
  name: 'mysql_get_foreign_keys',
  description: '获取表或所有表的外键约束信息',
  parameters: z.object({
    table_name: z.string().optional().describe('可选的特定表名，用于获取该表的外键信息')
  }),
  execute: async (args) => {
    try {
      let query = `
        SELECT
          TABLE_NAME,
          COLUMN_NAME,
          CONSTRAINT_NAME,
          REFERENCED_TABLE_NAME,
          REFERENCED_COLUMN_NAME,
          UPDATE_RULE,
          DELETE_RULE
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE TABLE_SCHEMA = DATABASE()
          AND REFERENCED_TABLE_NAME IS NOT NULL
      `;

      const params: string[] = [];
      if (args.table_name) {
        mysqlManager['validateTableName'](args.table_name);
        query += " AND TABLE_NAME = ?";
        params.push(args.table_name);
      }

      query += " ORDER BY TABLE_NAME, CONSTRAINT_NAME";

      const result = await mysqlManager.executeQuery(query, params.length > 0 ? params : undefined);
      return JSON.stringify(result, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_GET_FOREIGN_KEYS_FAILED} ${error.message}`);
    }
  }
});

// 创建表工具
mcp.addTool({
  name: 'mysql_create_table',
  description: '创建带有指定列和约束的新表',
  parameters: z.object({
    table_name: z.string().describe('要创建的表名'),
    columns: z.array(
      z.object({
        name: z.string(),
        type: z.string(),
        nullable: z.boolean().optional(),
        default: z.string().optional(),
        primary_key: z.boolean().optional(),
        auto_increment: z.boolean().optional()
      })
    ).describe('列定义数组')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);

      const columnDefs: string[] = [];
      args.columns.forEach(col => {
        mysqlManager['validateInput'](col.name, 'column_name');
        mysqlManager['validateInput'](col.type, 'column_type');

        let definition = `\`${col.name}\` ${col.type}`;

        if (col.nullable === false) {
          definition += " NOT NULL";
        }
        if (col.auto_increment) {
          definition += " AUTO_INCREMENT";
        }
        if (col.default) {
          definition += ` DEFAULT ${col.default}`;
        }

        columnDefs.push(definition);
      });

      const primaryKeys = args.columns
        .filter(col => col.primary_key)
        .map(col => col.name);

      if (primaryKeys.length > 0) {
        columnDefs.push(`PRIMARY KEY (\`${primaryKeys.join('`, `')}\`)`);
      }

      const query = `CREATE TABLE \`${args.table_name}\` (${columnDefs.join(', ')})`;
      const result = await mysqlManager.executeQuery(query);

      // 使用统一缓存清理
      mysqlManager.invalidateCaches("CREATE");

      return JSON.stringify({ [StringConstants.SUCCESS_KEY]: true, ...result }, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_CREATE_TABLE_FAILED} ${error.message}`);
    }
  }
});

// 删除表工具
mcp.addTool({
  name: 'mysql_drop_table',
  description: '从数据库删除（丢弃）表',
  parameters: z.object({
    table_name: z.string().describe('要删除的表名'),
    if_exists: z.boolean().optional().describe('使用IF EXISTS子句以避免表不存在时的错误')
  }),
  execute: async (args) => {
    try {
      mysqlManager['validateTableName'](args.table_name);

      const query = `DROP TABLE ${args.if_exists ? 'IF EXISTS' : ''} \`${args.table_name}\``;
      const result = await mysqlManager.executeQuery(query);

      // 使用统一缓存清理
      mysqlManager.invalidateCaches("DROP");

      return JSON.stringify({ [StringConstants.SUCCESS_KEY]: true, ...result }, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_DROP_TABLE_FAILED} ${error.message}`);
    }
  }
});

// 诊断连接工具
mcp.addTool({
  name: 'mysql_diagnose_connection',
  description: '诊断MySQL连接状态和配置',
  parameters: z.object({}),
  execute: async () => {
    try {
      const diagnosis: Record<string, any> = {
        [StringConstants.FIELD_CONNECTION_POOL_STATUS]: mysqlManager['connectionPool'].getStats(),
        [StringConstants.FIELD_CONFIG]: mysqlManager['configManager'].toObject(),
        [StringConstants.FIELD_PERFORMANCE_METRICS]: mysqlManager.getPerformanceMetrics(),
        enhanced_metrics: mysqlManager['enhancedMetrics'].getComprehensiveMetrics()
      };

      // Try to execute a simple connection test
      try {
        const connectionTestQuery = "SELECT 1 as test_connection";
        const testResult = await mysqlManager.executeQuery(connectionTestQuery);
        diagnosis[StringConstants.FIELD_CONNECTION_TEST] = {
          [StringConstants.STATUS_KEY]: StringConstants.STATUS_SUCCESS,
          [StringConstants.FIELD_RESULT]: testResult
        };
      } catch (error: any) {
        diagnosis[StringConstants.FIELD_CONNECTION_TEST] = {
          [StringConstants.STATUS_KEY]: StringConstants.STATUS_FAILED,
          [StringConstants.ERROR_KEY]: error.message
        };
      }

      return JSON.stringify(diagnosis, null, 2);
    } catch (error: any) {
      throw new Error(`${StringConstants.MSG_DIAGNOSE_FAILED} ${error.message}`);
    }
  }
});

// Graceful shutdown handler
process.on('SIGINT', async () => {
  console.error(`\n${StringConstants.MSG_SIGNAL_RECEIVED} SIGINT, ${StringConstants.MSG_GRACEFUL_SHUTDOWN}`);
  await mysqlManager.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.error(`\n${StringConstants.MSG_SIGNAL_RECEIVED} SIGTERM, ${StringConstants.MSG_GRACEFUL_SHUTDOWN}`);
  await mysqlManager.close();
  process.exit(0);
});

// Export the server and manager
export { mcp, mysqlManager };

// 启动服务器的函数
export async function startServer() {
  try {
    console.error(StringConstants.MSG_SERVER_RUNNING);
    await mcp.start();
  } catch (error: any) {
    console.error(`${StringConstants.MSG_SERVER_ERROR} ${error.message}`);
    await mysqlManager.close();
    process.exit(1);
  }
}

// 如果这是主模块则启动服务器
startServer();