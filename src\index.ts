// MySQL MCP服务器入口点
import { mcp, mysqlManager } from './mysqlMcpServer.js';
import { StringConstants } from './constants.js';

// 导出核心组件
export { mcp, mysqlManager };

// 启动服务器的函数
export async function startServer() {
  try {
    console.error(StringConstants.MSG_SERVER_RUNNING);
    await mcp.start();
  } catch (error: any) {
    console.error(`${StringConstants.MSG_SERVER_ERROR} ${error.message}`);
    await mysqlManager.close();
    process.exit(1);
  }
}

// 如果这是主模块则启动服务器
startServer();